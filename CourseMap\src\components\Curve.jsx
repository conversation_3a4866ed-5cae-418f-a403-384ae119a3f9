import React from "react";

const Curve = ({ handleAddCurve, startPoint, endPoint, fairwayLine }) => {
  // Base styles for the button (copied from ControlBox for parity)
  const buttonBaseStyle = {
    border: "none",
    borderRadius: "6px",
    padding: "2px 30px",
    cursor: "pointer",
    fontSize: "14px",
    fontWeight: "500",
    fontFamily: "'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif",
    transition: "all 0.2s ease-in-out",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "4px",
    minHeight: "40px",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  };

  const disabledButtonStyle = {
    ...buttonBaseStyle,
    backgroundColor: "#e5e7eb",
    color: "#9ca3af",
    cursor: "not-allowed",
    boxShadow: "none",
  };

  const enabledStyle = {
    ...buttonBaseStyle,
    backgroundColor: "#8b5cf6",
    color: "white",
    border: "2px solid #8b5cf6",
  };

  const isEnabled = !!(startPoint && endPoint && fairwayLine);

  return (
    <button
      onClick={handleAddCurve}
      disabled={!isEnabled}
      style={isEnabled ? enabledStyle : disabledButtonStyle}
      onMouseOver={(e) => {
        if (isEnabled) {
          e.target.style.backgroundColor = "#7c3aed";
          e.target.style.borderColor = "#7c3aed";
          e.target.style.transform = "translateY(-1px)";
        }
      }}
      onMouseOut={(e) => {
        if (isEnabled) {
          e.target.style.backgroundColor = "#8b5cf6";
          e.target.style.borderColor = "#8b5cf6";
          e.target.style.transform = "translateY(0)";
        }
      }}
    >
      <span style={{ fontSize: "16px" }}>↗️</span>
      Add Curve
    </button>
  );
};

export default Curve;
